// Property definitions for the Expert Advisor
#property copyright "Always Blue - Dynamic Strategy EA"
#property link      "https://alwaysblue.com"
#property version   "2.00"
#property description "Dynamic EA that fetches strategy configurations from JSON API"

// Include necessary libraries
#include <Trade\Trade.mqh>
#include <JAson.mqh>

// Create instances for trade operations and JSON parsing
CTrade trade;
CJAVal jsonParser;

//+------------------------------------------------------------------+
//| Input Parameters                                                  |
//+------------------------------------------------------------------+
input group "=== API Configuration ==="
input string APIKey = "";                    // Your API Key
input string StrategyID = "strategy_001";    // Strategy ID to fetch
input string ServerURL = "https://myserver.com/strategy"; // API Server URL
input int UpdateFrequency = 300;             // Strategy update frequency (seconds)

input group "=== Risk Management ==="
input double DefaultLotSize = 0.01;          // Default lot size if not specified in JSON
input double MaxDrawdownPct = 5.0;           // Maximum account drawdown percentage
input bool EnableRiskManagement = true;      // Enable dynamic risk management

input group "=== Trading Filters ==="
input bool EnableTimeFilter = true;          // Enable time-based trading filters
input bool EnableSpreadFilter = true;        // Enable spread-based filters
input int MaxSpreadPoints = 30;              // Maximum spread in points

input group "=== Logging and Debug ==="
input bool EnableDetailedLogging = true;     // Enable detailed logging
input bool EnableTestMode = false;           // Enable test mode with embedded JSON

//+------------------------------------------------------------------+
//| Global Variables                                                  |
//+------------------------------------------------------------------+
datetime lastAPICall = 0;                    // Last API call timestamp
datetime lastStrategyUpdate = 0;              // Last strategy update timestamp
bool strategyLoaded = false;                  // Flag indicating if strategy is loaded
string lastError = "";                        // Last error message
int retryCount = 0;                          // API retry counter
const int MAX_RETRIES = 3;                   // Maximum API retry attempts

//+------------------------------------------------------------------+
//| Enhanced Structures for Dynamic Strategy                         |
//+------------------------------------------------------------------+

/**
 * Structure to define entry rules from JSON
 */
struct EntryRule {
   string type;                              // Entry type (ma_crossover, rsi_filter, etc.)
   string symbol;                            // Trading symbol
   string timeframe;                         // Timeframe
   double fast_ma;                           // Fast MA period (for MA crossover)
   double slow_ma;                           // Slow MA period (for MA crossover)
   string ma_method;                         // MA method (SMA, EMA, etc.)
   int rsi_period;                           // RSI period
   double rsi_overbought;                    // RSI overbought level
   double rsi_oversold;                      // RSI oversold level
   string pattern;                           // Candlestick pattern name
   int confirm_candles;                      // Confirmation candles
   double atr_multiplier;                    // ATR multiplier for breakouts
   int atr_period;                           // ATR period
};

/**
 * Structure to define filters from JSON
 */
struct TradingFilters {
   string start_time;                        // Trading start time
   string end_time;                          // Trading end time
   double max_spread;                        // Maximum allowed spread
   double min_volatility;                    // Minimum volatility requirement
   double max_volatility;                    // Maximum volatility requirement
};

/**
 * Structure to define risk management from JSON
 */
struct RiskManagement {
   double lot_size;                          // Fixed lot size
   double max_account_drawdown_pct;          // Maximum account drawdown percentage
   bool use_percent_balance;                 // Use percentage of balance for sizing
   double risk_per_trade_pct;                // Risk percentage per trade
   int max_positions;                        // Maximum concurrent positions
};

/**
 * Structure to define exit rules from JSON
 */
struct ExitRules {
   double take_profit_pips;                  // Take profit in pips
   double stop_loss_pips;                    // Stop loss in pips
   bool trailing_stop_enabled;              // Enable trailing stop
   double trail_distance_pips;               // Trailing stop distance
   double trail_step_pips;                   // Trailing stop step
   bool time_exit_enabled;                   // Enable time-based exit
   int max_trade_duration_hours;             // Maximum trade duration
};

/**
 * Main Dynamic Strategy Structure
 * This structure holds the complete strategy configuration from JSON
 */
struct DynamicStrategy {
   string strategy_id;                       // Strategy identifier
   string description;                       // Strategy description
   EntryRule entries[];                      // Array of entry rules
   TradingFilters filters;                   // Trading filters
   RiskManagement risk;                      // Risk management settings
   ExitRules exit;                           // Exit rules
   string logic;                             // Logic to combine entry rules (AND/OR)
   string version;                           // Strategy version
   datetime last_updated;                    // Last update timestamp
};

// Global variables to hold the current strategy
DynamicStrategy currentStrategy;
string strategyJSON = "";                    // Raw JSON string for fallback

// Legacy structure for backward compatibility (will be removed)
struct IndicatorCondition {
   string name; // Name of the indicator (e.g., RSI, MA)
   double value; // Threshold value for the indicator
   string operator_condition; // Operator for comparison (e.g., >, <)
   int period; // Period for the indicator calculation
   int shift; // Shift for the indicator value
};

struct StrategyCondition {
   IndicatorCondition indicators[]; // Array of indicator conditions
   string logic; // Logic to combine conditions (AND/OR)
   double stop_loss; // Stop loss value
   double take_profit; // Take profit value
   bool is_buy; // Direction of the trade (buy/sell)
   string timeframe; // Timeframe for the strategy
   string symbol; // Trading symbol
   string version; // Version of the strategy
};

StrategyCondition legacyStrategy;            // For backward compatibility

//+------------------------------------------------------------------+
//| HTTP and JSON Handling Functions                                 |
//+------------------------------------------------------------------+

/**
 * Function to fetch strategy configuration from API
 * @param apiKey The API key for authentication
 * @param strategyId The strategy ID to fetch
 * @return true if successful, false otherwise
 */
bool FetchStrategyFromAPI(string apiKey, string strategyId) {
   if(EnableDetailedLogging) Print("Fetching strategy from API: ", strategyId);

   // Check if we need to update (respect update frequency)
   if(TimeCurrent() - lastAPICall < UpdateFrequency && strategyLoaded) {
      if(EnableDetailedLogging) Print("Strategy cache is still valid, skipping API call");
      return true;
   }

   string url = ServerURL + "?id=" + strategyId;
   string headers = "Authorization: Bearer " + apiKey + "\r\n";
   headers += "Content-Type: application/json\r\n";

   char post[], result[];
   string resultHeaders;

   ResetLastError();
   int response = WebRequest("GET", url, headers, 5000, post, result, resultHeaders);

   if(response == -1) {
      int error = GetLastError();
      lastError = "WebRequest failed with error: " + IntegerToString(error);
      if(EnableDetailedLogging) Print(lastError);

      if(error == 4014) {
         Alert("Please add the URL '" + ServerURL + "' to the list of allowed URLs in Tools->Options->Expert Advisors");
      }
      return false;
   }

   if(response != 200) {
      lastError = "HTTP error: " + IntegerToString(response);
      if(EnableDetailedLogging) Print(lastError);
      return false;
   }

   // Convert result to string
   strategyJSON = CharArrayToString(result, 0, -1, CP_UTF8);
   lastAPICall = TimeCurrent();

   if(EnableDetailedLogging) Print("Strategy JSON received: ", StringSubstr(strategyJSON, 0, 200), "...");

   return ParseStrategyJSON(strategyJSON);
}

/**
 * Function to parse JSON strategy configuration
 * @param jsonString The JSON string to parse
 * @return true if successful, false otherwise
 */
bool ParseStrategyJSON(string jsonString) {
   if(EnableDetailedLogging) Print("Parsing strategy JSON...");

   // Clear previous parser data
   jsonParser.Clear();

   // Parse JSON string
   if(!jsonParser.Deserialize(jsonString)) {
      lastError = "Failed to parse JSON string";
      if(EnableDetailedLogging) Print(lastError);
      return false;
   }

   // Extract strategy information using correct JAson.mqh syntax
   CJAVal* node = jsonParser.HasKey("strategy_id");
   if(node != NULL) {
      currentStrategy.strategy_id = node.ToStr();
   }

   node = jsonParser.HasKey("description");
   if(node != NULL) {
      currentStrategy.description = node.ToStr();
   }

   node = jsonParser.HasKey("version");
   if(node != NULL) {
      currentStrategy.version = node.ToStr();
   }

   node = jsonParser.HasKey("logic");
   if(node != NULL) {
      currentStrategy.logic = node.ToStr();
   }

   // Parse risk management
   node = jsonParser.HasKey("lot_size");
   if(node != NULL) {
      currentStrategy.risk.lot_size = node.ToDbl();
   }

   node = jsonParser.HasKey("max_account_drawdown_pct");
   if(node != NULL) {
      currentStrategy.risk.max_account_drawdown_pct = node.ToDbl();
   }

   node = jsonParser.HasKey("use_percent_balance");
   if(node != NULL) {
      currentStrategy.risk.use_percent_balance = node.ToBool();
   }

   node = jsonParser.HasKey("risk_per_trade_pct");
   if(node != NULL) {
      currentStrategy.risk.risk_per_trade_pct = node.ToDbl();
   }

   // Parse exit rules
   node = jsonParser.HasKey("take_profit_pips");
   if(node != NULL) {
      currentStrategy.exit.take_profit_pips = node.ToDbl();
   }

   node = jsonParser.HasKey("stop_loss_pips");
   if(node != NULL) {
      currentStrategy.exit.stop_loss_pips = node.ToDbl();
   }

   // Parse trailing stop settings
   node = jsonParser.HasKey("trailing_stop_enabled");
   if(node != NULL) {
      currentStrategy.exit.trailing_stop_enabled = node.ToBool();
   }

   node = jsonParser.HasKey("trail_distance_pips");
   if(node != NULL) {
      currentStrategy.exit.trail_distance_pips = node.ToDbl();
   }

   node = jsonParser.HasKey("trail_step_pips");
   if(node != NULL) {
      currentStrategy.exit.trail_step_pips = node.ToDbl();
   }

   currentStrategy.last_updated = TimeCurrent();
   strategyLoaded = true;

   if(EnableDetailedLogging) Print("Strategy parsed successfully: ", currentStrategy.strategy_id);
   return true;
}

/**
 * Simple JSON value extractor (placeholder until JAson.mqh is available)
 * @param jsonString The JSON string
 * @param key The key to extract
 * @return The extracted value as string
 */
string ExtractJSONValue(string jsonString, string key) {
   string searchPattern = "\"" + key + "\":";
   int startPos = StringFind(jsonString, searchPattern);
   if(startPos < 0) return "";

   startPos += StringLen(searchPattern);

   // Skip whitespace and quotes
   while(startPos < StringLen(jsonString) && (StringGetCharacter(jsonString, startPos) == ' ' || StringGetCharacter(jsonString, startPos) == '"')) {
      startPos++;
   }

   int endPos = startPos;
   bool inQuotes = false;

   // Find end of value
   while(endPos < StringLen(jsonString)) {
      ushort ch = StringGetCharacter(jsonString, endPos);
      if(ch == '"') inQuotes = !inQuotes;
      if(!inQuotes && (ch == ',' || ch == '}' || ch == ']')) break;
      endPos++;
   }

   string value = StringSubstr(jsonString, startPos, endPos - startPos);
   StringReplace(value, "\"", "");
   return value;
}

/**
 * Function to load embedded test strategy for Strategy Tester
 */
void LoadEmbeddedTestStrategy() {
   if(EnableDetailedLogging) Print("Loading embedded test strategy...");

   // Embedded JSON for testing when WebRequest is not available
   string testJSON = "{"
      "\"strategy_id\":\"test_ma_rsi_001\","
      "\"description\":\"MA crossover with RSI filter\","
      "\"version\":\"1.0\","
      "\"logic\":\"AND\","
      "\"lot_size\":0.01,"
      "\"max_account_drawdown_pct\":5.0,"
      "\"take_profit_pips\":30,"
      "\"stop_loss_pips\":15"
   "}";

   ParseStrategyJSON(testJSON);
}

/**
 * Function to load a hardcoded strategy based on a selector (legacy function).
 * This function populates the legacyStrategy variable with the selected strategy's parameters.
 *
 * @param selector The index of the strategy to load.
 */
void LoadLegacyStrategy(int selector) {
   if(selector == 1) {
      // Set the logic for combining conditions to AND
      legacyStrategy.logic = "AND";

      // Set the stop loss to 50 pips
      legacyStrategy.stop_loss = 50;

      // Set the take profit to 100 pips
      legacyStrategy.take_profit = 100;

      // Set the trade direction to buy
      legacyStrategy.is_buy = true;

      // Set the timeframe to 15 minutes
      legacyStrategy.timeframe = "M1";

      // Set the trading symbol
      legacyStrategy.symbol = "EURUSD";

      // Set the strategy version
      legacyStrategy.version = "1.0";

      // Define the number of indicator conditions
      int ind_count = 2;

      // Resize the indicators array to fit the number of conditions
      ArrayResize(legacyStrategy.indicators, ind_count);

      // Assign values to the first indicator condition
      legacyStrategy.indicators[0].name = "RSI"; // Relative Strength Index
      legacyStrategy.indicators[0].value = 30; // RSI threshold value
      legacyStrategy.indicators[0].operator_condition = "<"; // Condition is less than
      legacyStrategy.indicators[0].period = 14; // RSI period
      legacyStrategy.indicators[0].shift = 0; // No shift

      // Assign values to the second indicator condition
      legacyStrategy.indicators[1].name = "MA"; // Moving Average
      legacyStrategy.indicators[1].value = 1.2000; // MA threshold value
      legacyStrategy.indicators[1].operator_condition = ">"; // Condition is greater than
      legacyStrategy.indicators[1].period = 20; // MA period
      legacyStrategy.indicators[1].shift = 0; // No shift
   }
}

/**
 * Function called when the EA is initialized.
 * This function loads the dynamic strategy configuration and initializes the EA.
 *
 * @return INIT_SUCCEEDED on success, INIT_FAILED on failure.
 */
int OnInit() {
   Print("=== Always Blue Dynamic EA v2.00 Initializing ===");

   // Initialize global variables
   strategyLoaded = false;
   lastAPICall = 0;
   lastStrategyUpdate = 0;
   retryCount = 0;
   lastError = "";

   // Validate input parameters
   if(!ValidateInputParameters()) {
      Print("ERROR: Invalid input parameters");
      return INIT_PARAMETERS_INCORRECT;
   }

   // Initialize trade object
   trade.SetExpertMagicNumber(123456);
   trade.SetDeviationInPoints(3);
   trade.SetTypeFilling(ORDER_FILLING_FOK);

   // Load strategy configuration
   bool strategyLoadSuccess = false;

   if(EnableTestMode) {
      // Load embedded test strategy for Strategy Tester
      LoadEmbeddedTestStrategy();
      strategyLoadSuccess = true;
      Print("Test mode enabled - using embedded strategy");
   }
   else if(APIKey == "" || StrategyID == "") {
      // Fall back to legacy hardcoded strategy
      LoadLegacyStrategy(1);
      strategyLoadSuccess = true;
      Print("WARNING: No API key or Strategy ID provided - using legacy hardcoded strategy");
      Print("Loaded Legacy Strategy Version: ", legacyStrategy.version, " for ", legacyStrategy.symbol, " on ", legacyStrategy.timeframe);
   }
   else {
      // Try to fetch strategy from API
      strategyLoadSuccess = FetchStrategyFromAPI(APIKey, StrategyID);

      if(!strategyLoadSuccess) {
         Print("WARNING: Failed to fetch strategy from API - ", lastError);

         // Fall back to embedded test strategy
         LoadEmbeddedTestStrategy();
         strategyLoadSuccess = true;
         Print("Using embedded test strategy as fallback");
      }
      else {
         Print("Successfully loaded dynamic strategy: ", currentStrategy.strategy_id);
         Print("Strategy Description: ", currentStrategy.description);
         Print("Strategy Version: ", currentStrategy.version);
      }
   }

   if(!strategyLoadSuccess) {
      Print("ERROR: Failed to load any strategy configuration");
      return INIT_FAILED;
   }

   // Set up timer for periodic strategy updates (if using API)
   if(!EnableTestMode && APIKey != "" && StrategyID != "") {
      EventSetTimer(UpdateFrequency);
      Print("Timer set for strategy updates every ", UpdateFrequency, " seconds");
   }

   // Print initialization summary
   PrintInitializationSummary();

   Print("=== Always Blue Dynamic EA Initialization Complete ===");
   return INIT_SUCCEEDED;
}

/**
 * Validate input parameters
 * @return true if parameters are valid, false otherwise
 */
bool ValidateInputParameters() {
   if(UpdateFrequency < 60) {
      Print("ERROR: Update frequency must be at least 60 seconds");
      return false;
   }

   if(DefaultLotSize <= 0) {
      Print("ERROR: Default lot size must be greater than 0");
      return false;
   }

   if(MaxDrawdownPct <= 0 || MaxDrawdownPct > 100) {
      Print("ERROR: Max drawdown percentage must be between 0 and 100");
      return false;
   }

   if(MaxSpreadPoints < 0) {
      Print("ERROR: Max spread points cannot be negative");
      return false;
   }

   return true;
}

/**
 * Print initialization summary
 */
void PrintInitializationSummary() {
   Print("--- EA Configuration Summary ---");
   Print("API Key: ", (APIKey == "" ? "Not provided" : "Provided"));
   Print("Strategy ID: ", StrategyID);
   Print("Server URL: ", ServerURL);
   Print("Update Frequency: ", UpdateFrequency, " seconds");
   Print("Default Lot Size: ", DefaultLotSize);
   Print("Max Drawdown: ", MaxDrawdownPct, "%");
   Print("Risk Management: ", (EnableRiskManagement ? "Enabled" : "Disabled"));
   Print("Time Filter: ", (EnableTimeFilter ? "Enabled" : "Disabled"));
   Print("Spread Filter: ", (EnableSpreadFilter ? "Enabled" : "Disabled"));
   Print("Max Spread: ", MaxSpreadPoints, " points");
   Print("Test Mode: ", (EnableTestMode ? "Enabled" : "Disabled"));
   Print("Detailed Logging: ", (EnableDetailedLogging ? "Enabled" : "Disabled"));
   Print("--- End Configuration Summary ---");
}

/**
 * Function called on timer events for periodic strategy updates
 */
void OnTimer() {
   if(EnableDetailedLogging) Print("Timer event - checking for strategy updates");

   // Only update if we're using API and not in test mode
   if(!EnableTestMode && APIKey != "" && StrategyID != "") {
      bool updateSuccess = FetchStrategyFromAPI(APIKey, StrategyID);

      if(!updateSuccess) {
         retryCount++;
         if(retryCount >= MAX_RETRIES) {
            Print("WARNING: Maximum API retry attempts reached. Using cached strategy.");
            retryCount = 0; // Reset for next cycle
         }
      }
      else {
         retryCount = 0; // Reset retry counter on success
         lastStrategyUpdate = TimeCurrent();
      }
   }
}

/**
 * Function called when EA is deinitialized
 */
void OnDeinit(const int reason) {
   EventKillTimer();

   string reasonText = "";
   switch(reason) {
      case REASON_PROGRAM: reasonText = "EA was stopped by user"; break;
      case REASON_REMOVE: reasonText = "EA was removed from chart"; break;
      case REASON_RECOMPILE: reasonText = "EA was recompiled"; break;
      case REASON_CHARTCHANGE: reasonText = "Chart symbol or timeframe changed"; break;
      case REASON_CHARTCLOSE: reasonText = "Chart was closed"; break;
      case REASON_PARAMETERS: reasonText = "Input parameters changed"; break;
      case REASON_ACCOUNT: reasonText = "Account changed"; break;
      case REASON_TEMPLATE: reasonText = "Template changed"; break;
      case REASON_INITFAILED: reasonText = "Initialization failed"; break;
      case REASON_CLOSE: reasonText = "Terminal closing"; break;
      default: reasonText = "Unknown reason"; break;
   }

   Print("=== Always Blue Dynamic EA Deinitialized ===");
   Print("Reason: ", reasonText);
   Print("Total API calls made: ", (TimeCurrent() - 0) / UpdateFrequency); // Approximate
   Print("Last error: ", lastError);
}

/**
 * Function called on every tick.
 * This function evaluates dynamic strategy conditions and executes trades.
 */
void OnTick() {
   // Check if strategy is loaded
   if(!strategyLoaded && !EnableTestMode) {
      if(EnableDetailedLogging) Print("Strategy not loaded, skipping tick");
      return;
   }

   // Apply trading filters
   if(!PassTradingFilters()) {
      return;
   }

   // Evaluate strategy conditions
   bool signalFound = false;

   if(EnableTestMode || (APIKey != "" && StrategyID != "")) {
      // Use dynamic strategy evaluation
      signalFound = EvaluateDynamicStrategy();
   }
   else {
      // Fall back to legacy strategy evaluation
      signalFound = EvaluateLegacyStrategy();
   }

   // Execute trade if signal is found
   if(signalFound) {
      ExecuteDynamicTrade();
   }

   // Manage existing positions
   ManageExistingPositions();
}

//+------------------------------------------------------------------+
//| Strategy Evaluation Functions                                    |
//+------------------------------------------------------------------+

/**
 * Apply trading filters before evaluating strategy
 * @return true if all filters pass, false otherwise
 */
bool PassTradingFilters() {
   // Time filter
   if(EnableTimeFilter) {
      // For now, allow trading during market hours
      // This can be enhanced with specific time ranges from JSON
      datetime currentTime = TimeCurrent();
      MqlDateTime dt;
      TimeToStruct(currentTime, dt);

      // Basic market hours filter (can be made configurable)
      if(dt.hour < 7 || dt.hour > 17) {
         if(EnableDetailedLogging) Print("Time filter: Outside trading hours");
         return false;
      }
   }

   // Spread filter
   if(EnableSpreadFilter) {
      double spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
      if(spread > MaxSpreadPoints) {
         if(EnableDetailedLogging) Print("Spread filter: Spread too high (", spread, " > ", MaxSpreadPoints, ")");
         return false;
      }
   }

   // Account drawdown filter
   if(EnableRiskManagement) {
      double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
      double currentDrawdown = (accountBalance - accountEquity) / accountBalance * 100;

      if(currentDrawdown > MaxDrawdownPct) {
         if(EnableDetailedLogging) Print("Risk filter: Drawdown too high (", currentDrawdown, "% > ", MaxDrawdownPct, "%)");
         return false;
      }
   }

   return true;
}

/**
 * Evaluate dynamic strategy from JSON configuration
 * @return true if strategy conditions are met, false otherwise
 */
bool EvaluateDynamicStrategy() {
   if(!strategyLoaded) {
      if(EnableDetailedLogging) Print("Dynamic strategy not loaded");
      return false;
   }

   // For now, implement a basic evaluation
   // This will be expanded to handle multiple entry rules and complex logic

   // Check if we have any positions open (simple position management)
   if(PositionsTotal() > 0) {
      if(EnableDetailedLogging) Print("Position already open, skipping new signals");
      return false;
   }

   // Basic signal generation based on current strategy
   // This is a placeholder implementation that will be expanded

   // Simple moving average crossover example
   int fastMA_handle = iMA(Symbol(), PERIOD_CURRENT, 10, 0, MODE_SMA, PRICE_CLOSE);
   int slowMA_handle = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE);
   int rsi_handle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);

   double fastMA_buffer[], slowMA_buffer[], rsi_buffer[];
   if(CopyBuffer(fastMA_handle, 0, 0, 1, fastMA_buffer) <= 0 ||
      CopyBuffer(slowMA_handle, 0, 0, 1, slowMA_buffer) <= 0 ||
      CopyBuffer(rsi_handle, 0, 0, 1, rsi_buffer) <= 0) {
      if(EnableDetailedLogging) Print("Failed to copy indicator buffers");
      return false;
   }

   double fastMA = fastMA_buffer[0];
   double slowMA = slowMA_buffer[0];
   double rsi = rsi_buffer[0];

   // Check for bullish signal
   if(fastMA > slowMA && rsi < 70) {
      if(EnableDetailedLogging) Print("Dynamic strategy: Bullish signal detected");
      return true;
   }

   // Check for bearish signal
   if(fastMA < slowMA && rsi > 30) {
      if(EnableDetailedLogging) Print("Dynamic strategy: Bearish signal detected");
      return true;
   }

   return false;
}

/**
 * Evaluate legacy strategy conditions (backward compatibility)
 * @return true if the strategy conditions are met, false otherwise
 */
bool EvaluateLegacyStrategy() {
   // Check if the current timeframe and symbol match the strategy
   if(legacyStrategy.timeframe != PeriodToString(_Period) || legacyStrategy.symbol != Symbol())
      return false;

   // Initialize the result based on the logic
   bool result = (legacyStrategy.logic == "AND") ? true : false;

   // Loop through each indicator condition
   for(int i = 0; i < ArraySize(legacyStrategy.indicators); i++) {
      // Get the indicator value
      double ind_value = GetLegacyIndicatorValue(legacyStrategy.indicators[i]);

      // Compare the indicator value with the threshold value
      bool condition = CompareValues(ind_value, legacyStrategy.indicators[i].value, legacyStrategy.indicators[i].operator_condition);

      // Combine the results based on the logic
      if(legacyStrategy.logic == "AND") result = result && condition;
      else if(legacyStrategy.logic == "OR") result = result || condition;
   }
   // Return the evaluation result
   return result;
}

//+------------------------------------------------------------------+
//| Trade Execution Functions                                        |
//+------------------------------------------------------------------+

/**
 * Execute trade based on dynamic strategy configuration
 */
void ExecuteDynamicTrade() {
   if(EnableDetailedLogging) Print("Executing dynamic trade...");

   // Determine trade direction (simplified for now)
   int fastMA_handle = iMA(Symbol(), PERIOD_CURRENT, 10, 0, MODE_SMA, PRICE_CLOSE);
   int slowMA_handle = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE);

   double fastMA_buffer[], slowMA_buffer[];
   if(CopyBuffer(fastMA_handle, 0, 0, 1, fastMA_buffer) <= 0 ||
      CopyBuffer(slowMA_handle, 0, 0, 1, slowMA_buffer) <= 0) {
      if(EnableDetailedLogging) Print("Failed to copy MA buffers for trade execution");
      return;
   }

   double fastMA = fastMA_buffer[0];
   double slowMA = slowMA_buffer[0];

   bool isBuy = fastMA > slowMA;

   // Calculate lot size
   double lotSize = CalculateLotSize();

   // Calculate SL and TP
   double sl = 0, tp = 0;
   CalculateSlTp(isBuy, sl, tp);

   // Get current price
   double price = isBuy ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);

   // Execute the trade
   ulong ticket = 0;
   if(isBuy) {
      ticket = trade.Buy(lotSize, Symbol(), price, sl, tp, "Dynamic EA Buy");
   }
   else {
      ticket = trade.Sell(lotSize, Symbol(), price, sl, tp, "Dynamic EA Sell");
   }

   if(ticket > 0) {
      Print("Trade executed successfully: Ticket #", ticket, ", Direction: ", (isBuy ? "BUY" : "SELL"), ", Lot: ", lotSize);
   }
   else {
      Print("Trade execution failed: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   }
}

/**
 * Calculate appropriate lot size based on risk management
 * @return calculated lot size
 */
double CalculateLotSize() {
   double lotSize = DefaultLotSize;

   if(strategyLoaded && currentStrategy.risk.lot_size > 0) {
      lotSize = currentStrategy.risk.lot_size;
   }

   // Apply risk management if enabled
   if(EnableRiskManagement && strategyLoaded && currentStrategy.risk.use_percent_balance) {
      double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      double riskAmount = accountBalance * (currentStrategy.risk.risk_per_trade_pct / 100.0);

      // Calculate lot size based on risk amount and stop loss
      double stopLossPips = currentStrategy.exit.stop_loss_pips;
      if(stopLossPips > 0) {
         double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
         lotSize = riskAmount / (stopLossPips * pipValue);
      }
   }

   // Ensure lot size is within broker limits
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   if(EnableDetailedLogging) Print("Calculated lot size: ", lotSize);
   return lotSize;
}

/**
 * Calculate stop loss and take profit levels
 * @param isBuy true for buy orders, false for sell orders
 * @param sl reference to store stop loss price
 * @param tp reference to store take profit price
 */
void CalculateSlTp(bool isBuy, double &sl, double &tp) {
   double currentPrice = isBuy ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   double slPips = 15; // Default
   double tpPips = 30; // Default

   if(strategyLoaded) {
      slPips = currentStrategy.exit.stop_loss_pips;
      tpPips = currentStrategy.exit.take_profit_pips;
   }

   if(isBuy) {
      sl = (slPips > 0) ? currentPrice - (slPips * point * 10) : 0;
      tp = (tpPips > 0) ? currentPrice + (tpPips * point * 10) : 0;
   }
   else {
      sl = (slPips > 0) ? currentPrice + (slPips * point * 10) : 0;
      tp = (tpPips > 0) ? currentPrice - (tpPips * point * 10) : 0;
   }

   if(EnableDetailedLogging) Print("Calculated SL: ", sl, ", TP: ", tp);
}

/**
 * Manage existing positions (trailing stops, time exits, etc.)
 */
void ManageExistingPositions() {
   if(!strategyLoaded || !currentStrategy.exit.trailing_stop_enabled) {
      return; // No position management needed
   }

   for(int i = PositionsTotal() - 1; i >= 0; i--) {
      if(PositionGetSymbol(i) == Symbol()) {
         ulong ticket = PositionGetInteger(POSITION_TICKET);
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentSL = PositionGetDouble(POSITION_SL);
         bool isLong = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY;

         // Implement trailing stop
         double currentPrice = isLong ? SymbolInfoDouble(Symbol(), SYMBOL_BID) : SymbolInfoDouble(Symbol(), SYMBOL_ASK);
         double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
         double trailDistance = currentStrategy.exit.trail_distance_pips * point * 10;

         double newSL = 0;
         if(isLong) {
            newSL = currentPrice - trailDistance;
            if(newSL > currentSL + (currentStrategy.exit.trail_step_pips * point * 10)) {
               trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP));
               if(EnableDetailedLogging) Print("Trailing stop updated for ticket ", ticket, " to ", newSL);
            }
         }
         else {
            newSL = currentPrice + trailDistance;
            if(newSL < currentSL - (currentStrategy.exit.trail_step_pips * point * 10)) {
               trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP));
               if(EnableDetailedLogging) Print("Trailing stop updated for ticket ", ticket, " to ", newSL);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Legacy Support Functions                                         |
//+------------------------------------------------------------------+

/**
 * Function to get the value of an indicator (legacy version).
 * This function retrieves the value of the specified indicator.
 *
 * @param ind The indicator condition.
 * @return The value of the indicator.
 */
double GetLegacyIndicatorValue(IndicatorCondition &ind) {
   // Check if the indicator is RSI
   if(ind.name == "RSI") {
      int rsi_handle = iRSI(Symbol(), PERIOD_CURRENT, ind.period, PRICE_CLOSE);
      double rsi_buffer[];
      if(CopyBuffer(rsi_handle, 0, ind.shift, 1, rsi_buffer) > 0) {
         return rsi_buffer[0];
      }
      return 0;
   }

   // Check if the indicator is MA
   if(ind.name == "MA") {
      int ma_handle = iMA(Symbol(), PERIOD_CURRENT, ind.period, 0, MODE_SMA, PRICE_CLOSE);
      double ma_buffer[];
      if(CopyBuffer(ma_handle, 0, ind.shift, 1, ma_buffer) > 0) {
         return ma_buffer[0];
      }
      return 0;
   }

   // Return 0 if the indicator is not recognized
   return 0;
}

/**
 * Function to compare two values based on an operator.
 * This function compares two values using the specified operator.
 *
 * @param value1 The first value.
 * @param value2 The second value.
 * @param op The operator.
 * @return true if the comparison is true, false otherwise.
 */
bool CompareValues(double value1, double value2, string op) {
   // Check if the operator is greater than
   if(op == ">") return value1 > value2;

   // Check if the operator is less than
   if(op == "<") return value1 < value2;

   // Check if the operator is greater than or equal
   if(op == ">=") return value1 >= value2;

   // Check if the operator is less than or equal
   if(op == "<=") return value1 <= value2;

   // Check if the operator is equal
   if(op == "==") return MathAbs(value1 - value2) < 0.00001;

   // Return false if the operator is not recognized
   return false;
}

/**
 * Legacy function to execute a trade based on the hardcoded strategy.
 * This function executes a trade with the specified parameters.
 */
void ExecuteLegacyTrade() {
   // Calculate the stop loss price
   double sl = legacyStrategy.stop_loss * SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   // Calculate the take profit price
   double tp = legacyStrategy.take_profit * SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   // Get the current price
   double price = (legacyStrategy.is_buy) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);

   // Calculate the stop loss level
   double sl_price = (legacyStrategy.is_buy) ? (price - sl) : (price + sl);

   // Calculate the take profit level
   double tp_price = (legacyStrategy.is_buy) ? (price + tp) : (price - tp);

   // Execute the trade and print the ticket number
   ulong ticket = (legacyStrategy.is_buy) ? trade.Buy(0.1, Symbol(), price, sl_price, tp_price) : trade.Sell(0.1, Symbol(), price, sl_price, tp_price);
   if(ticket > 0) Print("Legacy trade executed: ", ticket);
}

//+------------------------------------------------------------------+
//| Utility Functions                                                |
//+------------------------------------------------------------------+

/**
 * Function to convert timeframe enum to string.
 * This function converts the timeframe enum to a string representation.
 *
 * @param period The timeframe enum.
 * @return The string representation of the timeframe.
 */
string PeriodToString(ENUM_TIMEFRAMES period) {
   // Convert the timeframe enum to a string
   switch(period) {
      case PERIOD_M1: return "M1"; // 1-minute timeframe
      case PERIOD_M5: return "M5"; // 5-minute timeframe
      case PERIOD_M15: return "M15"; // 15-minute timeframe
      case PERIOD_M30: return "M30"; // 30-minute timeframe
      case PERIOD_H1: return "H1"; // 1-hour timeframe
      case PERIOD_H4: return "H4"; // 4-hour timeframe
      case PERIOD_D1: return "D1"; // Daily timeframe
      case PERIOD_W1: return "W1"; // Weekly timeframe
      case PERIOD_MN1: return "MN1"; // Monthly timeframe
      default: return "Unknown"; // Unknown timeframe
   }
}

/**
 * Function to create a sample JSON strategy for testing
 * @return Sample JSON string
 */
string CreateSampleJSON() {
   string sampleJSON = "{"
      "\"strategy_id\":\"ma_rsi_combo_001\","
      "\"description\":\"MA crossover with RSI filter\","
      "\"version\":\"1.0\","
      "\"logic\":\"AND\","
      "\"entries\":["
         "{"
            "\"type\":\"ma_crossover\","
            "\"symbol\":\"EURUSD\","
            "\"timeframe\":\"H1\","
            "\"fast_ma\":10,"
            "\"slow_ma\":30,"
            "\"ma_method\":\"SMA\""
         "},"
         "{"
            "\"type\":\"rsi_filter\","
            "\"symbol\":\"EURUSD\","
            "\"timeframe\":\"H1\","
            "\"rsi_period\":14,"
            "\"rsi_overbought\":70,"
            "\"rsi_oversold\":30"
         "}"
      "],"
      "\"filters\":{"
         "\"time_of_day\":{"
            "\"start\":\"07:00\","
            "\"end\":\"17:00\""
         "},"
         "\"max_spread\":2"
      "},"
      "\"risk\":{"
         "\"lot_size\":0.01,"
         "\"max_account_drawdown_pct\":5,"
         "\"use_percent_balance\":false,"
         "\"risk_per_trade_pct\":1"
      "},"
      "\"exit\":{"
         "\"take_profit_pips\":30,"
         "\"stop_loss_pips\":15,"
         "\"trailing_stop\":{"
            "\"enabled\":true,"
            "\"trail_distance_pips\":10,"
            "\"trail_step_pips\":5"
         "}"
      "}"
   "}";

   return sampleJSON;
}

//+------------------------------------------------------------------+
//| End of Always Blue Dynamic EA v2.00                             |
//+------------------------------------------------------------------+
