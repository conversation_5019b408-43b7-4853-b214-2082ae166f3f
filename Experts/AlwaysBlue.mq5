// Property definitions for the Expert Advisor
#property copyright "Always Blue - Hardcoded Strategy EA"
#property link      "https://alwaysblue.com"
#property version   "1.01"

// Include the trade library for executing trades
#include <Trade\Trade.mqh>

// Create an instance of the CTrade class for trade operations
CTrade trade;

/**
 * Structure to define the conditions for an indicator.
 * This structure holds the necessary information to evaluate an indicator condition.
 */
struct IndicatorCondition {
   string name; // Name of the indicator (e.g., RSI, MA)
   double value; // Threshold value for the indicator
   string operator_condition; // Operator for comparison (e.g., >, <)
   int period; // Period for the indicator calculation
   int shift; // Shift for the indicator value

   /**
    * Overload assignment operator for easy copying of conditions.
    * This allows for simple assignment of one IndicatorCondition to another.
    */
   void operator=(const IndicatorCondition &other) {
      name = other.name;
      value = other.value;
      operator_condition = other.operator_condition;
      period = other.period;
      shift = other.shift;
   }
};

/**
 * Structure to define the strategy conditions.
 * This structure holds the necessary information to evaluate a trading strategy.
 */
struct StrategyCondition {
   IndicatorCondition indicators[]; // Array of indicator conditions
   string logic; // Logic to combine conditions (AND/OR)
   double stop_loss; // Stop loss value
   double take_profit; // Take profit value
   bool is_buy; // Direction of the trade (buy/sell)
   string timeframe; // Timeframe for the strategy
   string symbol; // Trading symbol (e.g., EURUSD)
   string version; // Version of the strategy
};

// Global variable to hold the current strategy
StrategyCondition currentStrategy;

/**
 * Function to load a hardcoded strategy based on a selector.
 * This function populates the currentStrategy variable with the selected strategy's parameters.
 *
 * @param selector The index of the strategy to load.
 */
void LoadStrategy(int selector) {
   if(selector == 1) {
      // Set the logic for combining conditions to AND
      currentStrategy.logic = "AND"; 
      
      // Set the stop loss to 50 pips
      currentStrategy.stop_loss = 50; 
      
      // Set the take profit to 100 pips
      currentStrategy.take_profit = 100; 
      
      // Set the trade direction to buy
      currentStrategy.is_buy = true; 
      
      // Set the timeframe to 15 minutes
      currentStrategy.timeframe = "M1"; 
      
      // Set the trading symbol to EURUSD
      currentStrategy.symbol = "EURUSD"; 
      // this can either be forced or should be 'current symbol'
      
      // Set the strategy version
      currentStrategy.version = "1.0"; 

      // Define the number of indicator conditions
      int ind_count = 2; 

      // Resize the indicators array to fit the number of conditions
      ArrayResize(currentStrategy.indicators, ind_count);

      // Assign values to the first indicator condition
      currentStrategy.indicators[0].name = "RSI"; // Relative Strength Index
      currentStrategy.indicators[0].value = 30; // RSI threshold value
      currentStrategy.indicators[0].operator_condition = "<"; // Condition is less than
      currentStrategy.indicators[0].period = 14; // RSI period
      currentStrategy.indicators[0].shift = 0; // No shift

      // Assign values to the second indicator condition
      currentStrategy.indicators[1].name = "MA"; // Moving Average
      currentStrategy.indicators[1].value = 1.2000; // MA threshold value
      currentStrategy.indicators[1].operator_condition = ">"; // Condition is greater than
      currentStrategy.indicators[1].period = 20; // MA period
      currentStrategy.indicators[1].shift = 0; // No shift
   }
}

/**
 * Function called when the script is initialized.
 * This function is used to load the strategy and print a confirmation message.
 *
 * @return INIT_SUCCEEDED on success.
 */
int OnInit() {
   // Load the first strategy
   LoadStrategy(1); 
   
   // Print a confirmation message with the loaded strategy's version, symbol, and timeframe
   Print("Loaded Strategy Version: ", currentStrategy.version, " for ", currentStrategy.symbol, " on ", currentStrategy.timeframe);
   
   // Return success code
   return(INIT_SUCCEEDED); 
}

/**
 * Function called on every tick.
 * This function checks if the strategy conditions are met and executes a trade if they are.
 */
void OnTick() {
   // Check if the strategy conditions are met
   if(EvaluateStrategy()) { 
      // Execute the trade if conditions are met
      ExecuteTrade(); 
   }
}

/**
 * Function to evaluate the strategy conditions.
 * This function checks if the current timeframe and symbol match the strategy and evaluates the indicator conditions.
 *
 * @return true if the strategy conditions are met, false otherwise.
 */
bool EvaluateStrategy() {
   // Check if the current timeframe and symbol match the strategy
   if(currentStrategy.timeframe != PeriodToString(Period()) || currentStrategy.symbol != Symbol())
      return false;
   
   // Initialize the result based on the logic
   bool result = (currentStrategy.logic == "AND") ? true : false; 
   
   // Loop through each indicator condition
   for(int i = 0; i < ArraySize(currentStrategy.indicators); i++) { 
      // Get the indicator value
      double ind_value = GetIndicatorValue(currentStrategy.indicators[i]); 
      
      // Compare the indicator value with the threshold value
      bool condition = CompareValues(ind_value, currentStrategy.indicators[i].value, currentStrategy.indicators[i].operator_condition); 
      
      // Combine the results based on the logic
      if(currentStrategy.logic == "AND") result = result && condition;
      else if(currentStrategy.logic == "OR") result = result || condition;
   }
   // Return the evaluation result
   return result; 
}

/**
 * Function to get the value of an indicator.
 * This function retrieves the value of the specified indicator.
 *
 * @param ind The indicator condition.
 * @return The value of the indicator.
 */
double GetIndicatorValue(IndicatorCondition &ind) {
   // Check if the indicator is RSI
   if(ind.name == "RSI") { 
      // Get the RSI handle
      int handle = iRSI(NULL, 0, ind.period, PRICE_CLOSE); 
      
      // Copy the RSI value
      double rsi[];
      CopyBuffer(handle, 0, ind.shift, 1, rsi); 
      
      // Return the RSI value
      return rsi[0]; 
   }
   // Return 0 if the indicator is not recognized
   return 0; 
}

/**
 * Function to compare two values based on an operator.
 * This function compares two values using the specified operator.
 *
 * @param value1 The first value.
 * @param value2 The second value.
 * @param op The operator.
 * @return true if the comparison is true, false otherwise.
 */
bool CompareValues(double value1, double value2, string op) {
   // Check if the operator is greater than
   if(op == ">") return value1 > value2; 
   
   // Check if the operator is less than
   if(op == "<") return value1 < value2; 
   
   // Return false if the operator is not recognized
   return false; 
}

/**
 * Function to execute a trade based on the strategy.
 * This function executes a trade with the specified parameters.
 */
void ExecuteTrade() {
   // Calculate the stop loss price
   double sl = currentStrategy.stop_loss * Point(); 
   
   // Calculate the take profit price
   double tp = currentStrategy.take_profit * Point(); 
   
   // Get the current price
   double price = (currentStrategy.is_buy) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID); 
   
   // Calculate the stop loss level
   double sl_price = (currentStrategy.is_buy) ? (price - sl) : (price + sl); 
   
   // Calculate the take profit level
   double tp_price = (currentStrategy.is_buy) ? (price + tp) : (price - tp); 
   
   // Execute the trade and print the ticket number
   ulong ticket = (currentStrategy.is_buy) ? trade.Buy(0.1, NULL, price, sl_price, tp_price) : trade.Sell(0.1, NULL, price, sl_price, tp_price);
   if(ticket > 0) Print("Trade executed: ", ticket);
}

/**
 * Function to convert timeframe enum to string.
 * This function converts the timeframe enum to a string representation.
 *
 * @param period The timeframe enum.
 * @return The string representation of the timeframe.
 */
string PeriodToString(ENUM_TIMEFRAMES period) {
   // Convert the timeframe enum to a string
   switch(period) {
      case PERIOD_M1: return "M1"; // 1-minute timeframe
      case PERIOD_M15: return "M15"; // 15-minute timeframe
      case PERIOD_H1: return "H1"; // 1-hour timeframe
      default: return "Unknown"; // Unknown timeframe
   }
}
