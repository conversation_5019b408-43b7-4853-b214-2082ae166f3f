# Always Blue Dynamic EA v2.00 - Documentation

## Overview

The Always Blue Dynamic EA has been transformed from a static, hardcoded strategy EA into a fully dynamic trading system that fetches strategy configurations from JSON APIs. This allows traders to modify strategies without recompiling the EA, enabling rapid strategy testing and deployment.

## Key Features

### 🚀 **Dynamic Strategy Loading**
- Fetches strategy configurations from external APIs
- Supports JSON-based strategy definitions
- Real-time strategy updates without EA recompilation
- Fallback to embedded test strategies for Strategy Tester

### 🛡️ **Advanced Risk Management**
- Dynamic lot sizing (fixed, percentage-based, risk-based)
- Account drawdown protection
- Position exposure limits
- Configurable risk parameters via JSON

### 📊 **Flexible Strategy Dispatch**
- Support for multiple indicator types (MA, RSI, ATR, etc.)
- Candlestick pattern recognition framework
- Logical combinations (AND/OR) of conditions
- Extensible architecture for custom indicators

### ⚡ **Smart Trade Execution**
- Dynamic SL/TP calculation from JSON parameters
- Trailing stop management
- Time-based exits
- Signal-based position management

### 🔍 **Comprehensive Filtering**
- Time-based trading windows
- Spread filtering
- Volatility conditions
- Market sentiment filters

## Installation & Setup

### 1. File Structure
```
MQL5/
├── Experts/
│   └── AlwaysBlue.mq5          # Main EA file
├── Include/
│   └── JAson.mqh               # JSON parsing library
└── AlwaysBlueDynamicEA_Documentation.md
```

### 2. Required Libraries
- **Trade.mqh**: Standard MQL5 trading library (included with MT5)
- **JAson.mqh**: JSON parsing library (provided)

### 3. WebRequest Configuration
For live API usage, you must configure allowed URLs:
1. Go to **Tools → Options → Expert Advisors**
2. Check **"Allow WebRequest for listed URLs"**
3. Add your API server URL (e.g., `https://myserver.com`)

## Configuration Parameters

### API Configuration
- **APIKey**: Your unique API authentication key
- **StrategyID**: Strategy identifier to fetch from server
- **ServerURL**: API endpoint URL
- **UpdateFrequency**: Strategy update interval (seconds, minimum 60)

### Risk Management
- **DefaultLotSize**: Fallback lot size if not specified in JSON
- **MaxDrawdownPct**: Maximum account drawdown percentage (1-100)
- **EnableRiskManagement**: Enable/disable dynamic risk controls

### Trading Filters
- **EnableTimeFilter**: Enable time-based trading restrictions
- **EnableSpreadFilter**: Enable spread-based filtering
- **MaxSpreadPoints**: Maximum allowed spread in points

### Debug & Testing
- **EnableDetailedLogging**: Enable comprehensive logging
- **EnableTestMode**: Use embedded JSON for Strategy Tester

## JSON Strategy Format

### Basic Structure
```json
{
  "strategy_id": "ma_rsi_combo_001",
  "description": "MA crossover with RSI filter",
  "version": "1.0",
  "logic": "AND",
  "entries": [...],
  "filters": {...},
  "risk": {...},
  "exit": {...}
}
```

### Entry Rules
```json
"entries": [
  {
    "type": "ma_crossover",
    "symbol": "EURUSD",
    "timeframe": "H1",
    "fast_ma": 10,
    "slow_ma": 30,
    "ma_method": "SMA"
  },
  {
    "type": "rsi_filter",
    "symbol": "EURUSD",
    "timeframe": "H1",
    "rsi_period": 14,
    "rsi_overbought": 70,
    "rsi_oversold": 30
  }
]
```

### Risk Management
```json
"risk": {
  "lot_size": 0.01,
  "max_account_drawdown_pct": 5,
  "use_percent_balance": false,
  "risk_per_trade_pct": 1,
  "max_positions": 3
}
```

### Exit Rules
```json
"exit": {
  "take_profit_pips": 30,
  "stop_loss_pips": 15,
  "trailing_stop": {
    "enabled": true,
    "trail_distance_pips": 10,
    "trail_step_pips": 5
  },
  "time_exit": {
    "enabled": false,
    "max_duration_hours": 24
  }
}
```

### Trading Filters
```json
"filters": {
  "time_of_day": {
    "start": "07:00",
    "end": "17:00"
  },
  "max_spread": 2,
  "min_volatility": 0.5,
  "max_volatility": 3.0
}
```

## Supported Entry Types

### 1. Moving Average Crossover
```json
{
  "type": "ma_crossover",
  "fast_ma": 10,
  "slow_ma": 30,
  "ma_method": "SMA"
}
```

### 2. RSI Filter
```json
{
  "type": "rsi_filter",
  "rsi_period": 14,
  "rsi_overbought": 70,
  "rsi_oversold": 30
}
```

### 3. Candlestick Patterns (Framework)
```json
{
  "type": "candlestick_pattern",
  "pattern": "hammer",
  "confirm_candles": 1
}
```

### 4. ATR Breakout (Framework)
```json
{
  "type": "atr_breakout",
  "atr_period": 14,
  "multiplier": 1.5
}
```

## Operating Modes

### 1. Live API Mode
- **Requirements**: Valid APIKey and StrategyID
- **Behavior**: Fetches strategies from external server
- **Updates**: Automatic based on UpdateFrequency
- **Fallback**: Embedded test strategy on API failure

### 2. Test Mode
- **Requirements**: EnableTestMode = true
- **Behavior**: Uses embedded JSON strategy
- **Updates**: No external calls
- **Use Case**: Strategy Tester compatibility

### 3. Legacy Mode
- **Requirements**: Empty APIKey or StrategyID
- **Behavior**: Uses hardcoded strategy from original EA
- **Updates**: None
- **Use Case**: Backward compatibility

## Error Handling

### API Failures
- Automatic retry with exponential backoff
- Maximum retry attempts (configurable)
- Fallback to cached/embedded strategies
- Detailed error logging

### JSON Parsing Errors
- Schema validation
- Graceful degradation
- Error reporting in logs
- Fallback to default values

### Trade Execution Errors
- Retry mechanisms for temporary failures
- Detailed error codes and descriptions
- Position state tracking
- Recovery procedures

## Logging & Monitoring

### Log Levels
- **Basic**: Trade executions, strategy loads, errors
- **Detailed**: API calls, JSON parsing, filter results
- **Debug**: Internal state changes, calculations

### Key Metrics
- API call frequency and success rate
- Strategy update timestamps
- Trade execution statistics
- Risk management triggers

## Best Practices

### 1. API Design
- Use HTTPS for secure communication
- Implement proper authentication
- Include version control in JSON
- Provide schema validation

### 2. Risk Management
- Always set maximum drawdown limits
- Use appropriate position sizing
- Monitor correlation between strategies
- Implement circuit breakers

### 3. Testing
- Test with embedded strategies first
- Validate JSON schemas thoroughly
- Use Strategy Tester for backtesting
- Monitor live performance closely

### 4. Deployment
- Start with small position sizes
- Monitor API reliability
- Have fallback strategies ready
- Keep detailed logs

## Troubleshooting

### Common Issues

1. **WebRequest Error 4014**
   - Solution: Add API URL to allowed URLs list

2. **JSON Parsing Failures**
   - Check JSON syntax and schema
   - Verify all required fields are present

3. **No Trades Executed**
   - Check trading filters
   - Verify strategy conditions
   - Review risk management settings

4. **API Connection Issues**
   - Verify internet connectivity
   - Check API server status
   - Review authentication credentials

## Future Enhancements

### Planned Features
- Advanced pattern recognition
- Machine learning integration
- Multi-timeframe analysis
- Portfolio management
- Real-time performance analytics

### Extensibility
The EA architecture supports easy extension for:
- Custom indicators
- New entry/exit types
- Advanced filters
- External data sources
- Third-party integrations

## Support & Updates

For technical support, feature requests, or bug reports, please refer to the project documentation or contact the development team.

**Version**: 2.00  
**Last Updated**: 2025-01-23  
**Compatibility**: MetaTrader 5 Build 4000+
