{"strategy_id": "ma_rsi_combo_001", "description": "Moving Average crossover with RSI filter - Conservative approach", "version": "1.0.0", "logic": "AND", "meta": {"created_by": "Always Blue Trading System", "created_date": "2025-01-23", "symbols": ["EURUSD", "GBPUSD", "USDJPY"], "timeframes": ["H1", "H4"], "risk_level": "Conservative"}, "entries": [{"type": "ma_crossover", "symbol": "EURUSD", "timeframe": "H1", "fast_ma": 10, "slow_ma": 30, "ma_method": "SMA", "shift": 0, "weight": 1.0}, {"type": "rsi_filter", "symbol": "EURUSD", "timeframe": "H1", "rsi_period": 14, "rsi_overbought": 70, "rsi_oversold": 30, "shift": 0, "weight": 0.8}], "filters": {"time_of_day": {"enabled": true, "start": "07:00", "end": "17:00", "timezone": "GMT"}, "spread_filter": {"enabled": true, "max_spread_pips": 2.0, "max_spread_percent": 0.1}, "volatility_filter": {"enabled": true, "min_atr": 0.001, "max_atr": 0.005, "atr_period": 14}, "news_filter": {"enabled": false, "avoid_high_impact": true, "buffer_minutes": 30}, "correlation_filter": {"enabled": false, "max_correlation": 0.8, "symbols": ["EURUSD", "GBPUSD"]}}, "risk": {"lot_sizing": {"method": "fixed", "lot_size": 0.01, "use_percent_balance": false, "risk_per_trade_pct": 1.0, "max_lot_size": 0.1, "min_lot_size": 0.01}, "position_management": {"max_positions": 3, "max_positions_per_symbol": 1, "max_daily_trades": 10, "max_weekly_trades": 50}, "drawdown_protection": {"max_account_drawdown_pct": 5.0, "max_daily_loss_pct": 2.0, "stop_trading_on_limit": true, "recovery_threshold_pct": 1.0}, "correlation_limits": {"max_correlated_exposure": 0.05, "correlation_threshold": 0.7}}, "exit": {"take_profit": {"enabled": true, "take_profit_pips": 30, "use_atr_multiple": false, "atr_multiple": 2.0, "partial_close": {"enabled": false, "first_target_pct": 50, "first_target_pips": 15}}, "stop_loss": {"enabled": true, "stop_loss_pips": 15, "use_atr_multiple": false, "atr_multiple": 1.0, "breakeven": {"enabled": true, "trigger_pips": 10, "offset_pips": 2}}, "trailing_stop": {"enabled": true, "trail_distance_pips": 10, "trail_step_pips": 5, "start_after_pips": 15, "use_atr_based": false, "atr_multiple": 1.5}, "time_exit": {"enabled": false, "max_trade_duration_hours": 24, "exit_on_session_close": false, "session_close_time": "17:00"}, "signal_exit": {"enabled": false, "exit_on_reverse_signal": true, "exit_on_neutral_signal": false, "confirmation_bars": 1}}, "schedule": {"update_frequency": {"strategy_update_minutes": 60, "market_data_update_seconds": 30, "risk_check_minutes": 5}, "trading_sessions": [{"name": "London", "start": "08:00", "end": "16:00", "enabled": true, "weight": 1.0}, {"name": "New York", "start": "13:00", "end": "21:00", "enabled": true, "weight": 0.8}], "holidays": {"respect_broker_holidays": true, "custom_holidays": []}}, "notifications": {"trade_notifications": {"enabled": true, "on_entry": true, "on_exit": true, "on_error": true}, "risk_notifications": {"enabled": true, "on_drawdown_limit": true, "on_position_limit": true, "on_correlation_limit": true}, "system_notifications": {"enabled": true, "on_strategy_update": false, "on_connection_loss": true, "on_api_error": true}}, "backtesting": {"optimization_parameters": [{"parameter": "fast_ma", "min": 5, "max": 20, "step": 1}, {"parameter": "slow_ma", "min": 20, "max": 50, "step": 5}, {"parameter": "rsi_period", "min": 10, "max": 20, "step": 2}], "test_settings": {"model": "Every tick", "spread": "Current", "commission": 0.0, "initial_deposit": 10000, "leverage": 100}}, "performance_targets": {"monthly_return_target": 5.0, "max_monthly_drawdown": 3.0, "win_rate_target": 60.0, "profit_factor_target": 1.5, "sharpe_ratio_target": 1.2}, "version_control": {"schema_version": "2.0", "last_modified": "2025-01-23T10:00:00Z", "checksum": "abc123def456", "compatibility": {"min_ea_version": "2.00", "mt5_build": "4000+"}}}