You are an MQL5 Expert Advisor developer.  
Goal: Build an EA that:

1. Accepts user inputs:
   • API key (string)
   • Strategy ID (string or int)
2. On EA initialization (OnInit):
   • Sends HTTP request to server:
   – Method: GET or POST (depending on API spec)
   – Auth header or query param with API key
   – URL: e.g. "https://myserver.com/strategy?id={StrategyID}"
   • Parses JSON response using a JSON parser class (e.g., CJsonNode)
   • JSON payload may contain nested structures:
   {
   "entries": [
   {"type":"rsi_crossover","symbol":"EURUSD","params":{...}},
   {"type":"candlestick","pattern":"hammer","timeframe":"H1",...}
   ],
   "risk": {"fixed_lot":0.1,"max_drawdown_pct":2},
   "filters": [ ... ],
   "exit": {...}
   }
3. In OnCalculate or OnTick:
   • For each `entry` rule in JSON:
   – Dispatch control based on rule.type
   _ If built-in indicator (e.g. "ma_cross"):
   – Call iMA / iRSI with params from JSON
   – Evaluate cross / threshold
   _ If price action:
   – Evaluate candle pattern using OHLC data \* If custom logic:
   – Invoke custom handler defined in code
   – If condition met and risk rules validated:
   – Execute trade (OrderSend) with JSON-specified SL, TP, lot sizing
   • Apply filters (e.g. only trade at certain time, spread/volatility condition)
4. In risk manager:
   • Handle fixed lot or adaptive sizing based on account balance/drawdown
5. In trade exit logic:
   • Monitor existing orders or positions
   • Apply exit rules from JSON (e.g., trailing stop, reverse signal)
6. Implement error handling:
   • Retry HTTP fetch failures with back-off
   • Log parsing errors, JSON schema mismatches
7. Include a test harness:
   • Backtest with "Every Tick" mode
   • Simulate multiple JSON strategies via Strategy Tester

✔️ Key features to implement:
– CJsonNode-type JSON parser for MQL5 :contentReference[oaicite:1]{index=1}  
– HTTP/WebRequest usage and allowed URL setup :contentReference[oaicite:2]{index=2}  
– Indicator dispatch patterns: built‑in and price-action  
– Modular command dispatch: `switch(rule.type)`  
– Dynamic risk sizing and filters  
– Strategy/Test harness in EA for Strategy Tester and forward testing

---

### ❓Questions to Clarify

1. What JSON formats/formats should be supported? Please share an example.
2. Should indicator handling be open‑ended (via handlers/plugins)?
3. Would your server provide meta like "buy" or rule parameters to calculate signals?
4. How should parameter updates at runtime be handled (fetch per day? On signal)?
5. What environment are you running the EA in (live account, demo, VPS)?

---

## ✅ Expected Benefits

-   **Fully dynamic**: Swap strategies simply by changing JSON payload—no recompilation.
-   **Extensible**: Supports built‑in, PA, and custom handlers.
-   **Scalable**: Add new types or nested structures by updating JSON schema and handler logic.
-   **Testable**: Each JSON can be backtested via MT5 Strategy Tester with tick data.

🧪 Simulation Output (What LLM Should Produce Code‑wise)
JSON parser (CJsonNode) code snippet and usage example.

WebRequest logic with HTTP GET/POST, error checking, allowed URL docs.

Strategy dispatch skeleton:

for(int i=0; i<entries.Size(); i++) {
JsValue rule = entries[i];
string type = rule["type"].ToStr();
if(type=="ma*cross") { /* call iMA, evaluate crossover _/ }
else if(type=="rsi") { /_ iRSI logic _/ }
else if(type=="candlestick") { /_ OHLC pattern detection \_/ }
// Place trades dynamically
}
Modular risk manager and trade executor.

Instructions for backtesting and how to structure inputs.

1. 📌 Problem Statement
   Current Challenge: Traders must install or code separate EAs for each strategy. Making changes requires recompilation, redeployment, and tester/backtester setup.

Your Vision: A single EA binary where traders enter only an API key + strategy ID. At runtime, the EA fetches a JSON strategy profile from a server, interprets nested logic (indicators, price action, risk rules), and executes trades dynamically—no code changes required.

2. 🧭 Context & Competitive Analysis
   A. Traditional MT5 EA Development
   Static design: Strategies hard-coded in MQL5; updates require recompilation.

Example: A standard EA uses a moving average crossover or RSI threshold, compiled into the EA before deployment

Backtesting & debugging: Done in MetaEditor + Strategy Tester with manual builds per strategy

B. Traditional & Community Hybrid Tools
Utilities like FXDreema enable visual EA creation but rely on compiled logic or templated blocks; limited runtime strategy swapping

C. Emerging Dynamic Approaches
MQL5 WebRequest and JSON parsing allow runtime strategy loading, but few implementations combine full trade execution from JSON

Custom EAs that connect to external APIs (e.g., Gemini Flash) show feasibility

3. 🎯 Objectives
   Single EA binary interprets JSON strategies dynamically.

Strategy interchangeability at runtime: fetch by API key + ID.

JSON-driven logic supports built-in indicators, price patterns, custom rules, risk controls, and exit strategies.

Robust runtime behavior: error handling, schema validation, retry logic.

Full backtesting support: Strategy Tester-compatible with logic that handles WebRequest limitations.

4. 🛠 Functional Requirements
   Feature Description
   Inputs API Key (string), Strategy ID (string/int)
   Initialization (OnInit)

Perform WebRequest to fetch JSON, with API key auth

Parse nested JSON using CJsonNode or similar

Validate schema (entry rules, indicators, filters, risk, exits) |
| Signal Logic (OnTick / OnCalculate) |

Loop through entries[]: dispatch indicator (iMA, iRSI) or price action rule

Evaluate JSON-defined conditions, nested parameter sets

Check filters: time, spread, volatility |
| Trade Execution |

Send orders with JSON-lot-sizing, SL/TP

Use risk manager logic (fixed lot, % balance, drawdown cap) |
| Exit Management |

Monitor open trades

Apply JSON exit instructions: trailing stops, cross signals, time expiry |
| Error Handling |

On failure to fetch/parse: retry with backoff, disable EA if critical

Log errors and malformed JSON |
| Tester Compatibility |

WebRequest disabled in backtest; fallback to embedded test JSON or simulated fetch

| Code Extensibility |

Modular structure: IndicatorHandlers, PriceActionHandlers, RiskManager, TradeManager |
| Logging & Monitoring |

On-screen and terminal logging for fetched config, signal triggers, trades |
| Update frequency |

Configurable: fetch on init, on-bar, or time-based schedule |

https://medium.com/%40naveensanjula/step-by-step-guide-to-writing-an-expert-advisor-in-mql5-for-beginners-6512154a20b

https://docsbot.ai/prompts/technical/mt5-ea-development-guide-1?utm_source=chatgpt.com

https://www.reddit.com/r/algotrading/comments/1lnpiag/built_an_orb_ea_for_mt5_what_strategies_am_i/?utm_source=chatgpt.com

https://stackoverflow.com/questions/75292676/testing-webrequest-on-mt5?utm_source=chatgpt.com

5. 🏗 Non-Functional Requirements
   Performance: Minimal latency to avoid tick delays. JSON parser optimized.

Reliability: Handles 401s, timeouts, server errors gracefully.

Security: HTTPS, sanitized JSON, only allowed domains specified.

Testability: Full compatibility with MT5 Strategy Tester and real market.

Configurability: Settings available in EA inputs; log verbosity adjustable.

Maintainability: Layered code base with clear modules and documentation.

6. 🗓 Milestones & Deliverables
   Design & JSON Schema – Define structure for entries, filters, risk, exit.

HTTP & JSON Layer – Build WebRequest, parser, with error handling.

Indicator & Price Action Module – Dispatch built-in signals and pattern recognition.

Trade & Risk Execution Engine – Handle orders and dynamic sizing.

Exit Engine – Manage exits and trade lifecycle.

Testing Setup – Embedded test JSON for backtest environment.

Logging UI & Monitoring – Messages and status reporting.

Demo & Strategy Implementation – Sample JSON strategies (MA crossover, RSI reversal).

Documentation & Release – PRD, user guide, JSON template guide.

7. 🌐 References & Validation
   Traditional EA workflows: scripting strategies in MQL5 before compile
   https://www.mql5.com/en/articles/100?utm_source=chatgpt.com
   https://medium.com/%40naveensanjula/step-by-step-guide-to-writing-an-expert-advisor-in-mql5-for-beginners-6512154a20b?utm_source=chatgpt.com

Dynamic JSON-driven indicators/API use cases in MQL5
https://www.mql5.com/en/articles/14108?utm_source=chatgpt.com
https://www.mql5.com/en/articles/16446?utm_source=chatgpt.com

WebRequest + key-handling patterns; watch for backtest limitations
https://stackoverflow.com/questions/75292676/testing-webrequest-on-mt5?utm_source=chatgpt.com
https://community.openai.com/t/mql5-webrequest-always-returns-you-didnt-provide-an-api-key-authorization-header-dropped/1083783?utm_source=chatgpt.com

Real-world examples like Gemini Flash EA demonstrate feasibility
https://www.forexfactory.com/thread/1343918-experimental-mt5-ea-using-gemini-flash-api?utm_source=chatgpt.com

8. 📝 Next Steps
   Synchronize: Review JSON schema and pipeline structure.

Prototype: Focus first on the HTTP/JSON fetch and parsing.

Module-by-module: Build and test handler components in succession.

Backtest: Use embedded JSON stubs; simulate real-time fetch logic.

Deploy: Test on demo/live environments, iteratively refine.

This PRD captures the full scope—from problem statement, competitive landscape, functional/non-functional needs, to milestones.

Here’s an example of a JSON strategy payload that your dynamic EA could fetch and interpret—a flexible, nested structure defining entry rules, risk management, filters, and exits:

json
Copy
Edit
{
"strategy_id": "ma_rsi_combo_001",
"description": "MA crossover with RSI filter",
"entries": [
{
"type": "ma_crossover",
"symbol": "EURUSD",
"timeframe": "H1",
"fast_ma": 10,
"slow_ma": 30,
"ma_method": "SMA"
},
{
"type": "rsi_filter",
"symbol": "EURUSD",
"timeframe": "H1",
"rsi_period": 14,
"rsi_overbought": 70,
"rsi_oversold": 30
}
],
"filters": {
"time_of_day": {
"start": "07:00",
"end": "17:00"
},
"max_spread": 2
},
"risk": {
"lot_size": 0.1,
"max_account_drawdown_pct": 2,
"use_percent_balance": true
},
"exit": {
"take_profit_pips": 30,
"stop_loss_pips": 15,
"trailing_stop": {
"enabled": true,
"trail_distance_pips": 10,
"trail_step_pips": 5
}
}
}
🧠 How this maps to your EA architecture
entries:

ma_crossover: EA calculates two moving averages and generates a crossover signal.

rsi_filter: EA checks if RSI is within defined thresholds before entering.

filters:

Trades only executed between 07:00–17:00 server time; ignores signals when spread > 2 pips.

risk:

Uses fixed 0.1 lots; optional %‑based sizing; protects up to 2% account drawdown.

exit:

Sets TP/SL on entry.

Enables trailing stop if price moves favorably.

⚙️ Customizable Extensions
You can add more complex layers easily, like:

Price‑action entries:

json
Copy
Edit
{
"type": "candlestick_pattern",
"pattern": "hammer",
"confirm_candles": 1
}
Volatility triggers:

json
Copy
Edit
{
"type": "atr_breakout",
"period": 14,
"multiplier": 1.5
}
Nested logical structures:
Support "logic": "AND" or "OR" to combine rules inside entries.

✅ Production‑ready JSON schema tips
Include meta fields: versioning, symbols list, dynamic lot sizing rules:

json
Copy
Edit
"meta": {
"version": "1.0.0",
"symbols": ["EURUSD","USDJPY"]
}
Allow lists for multi-symbol ops:

json
Copy
Edit
"entries": [
{ "type":"ma_crossover", "symbols":["EURUSD","GBPUSD"], ... }
]
Support dynamic parameter updates:
Add "schedule": {"fetch_interval_min":60} to trigger config redraw.

This JSON template shows just how flexible and extensible your EA architecture can be. You don't need new EA versions for strategy tweaks—just update JSON rules on the server, redeploy them, and your EA adopts the logic live.
